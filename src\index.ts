import Phaser from 'phaser';
import PreloadScene from './scenes/PreloadScene';
import GameStartScene from './scenes/GameStartScene';
import GameScene from './scenes/GameScene';
import GameEndScene from './scenes/GameEndScene';

document.addEventListener('DOMContentLoaded', () => {
  // Check if the container exists
  const container = document.getElementById('game-container');
  if (!container) {
    console.error('Game container not found!');
    return;
  }

  const config: Phaser.Types.Core.GameConfig = {
    type: Phaser.AUTO,
    width: 540,
    height: 960,
    backgroundColor: '#0E0F1E',
    parent: 'game-container',
    scene: [PreloadScene, GameStartScene, GameScene, GameEndScene],
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { x: 0, y: 0 },
        debug: false
      }
    },
    scale: {
      mode: Phaser.Scale.EXPAND,
      autoCenter: Phaser.Scale.CENTER_BOTH,
    },
    // Mobile optimizations
    render: {
      antialias: true,
      powerPreference: 'high-performance'  // Request more GPU power
    },
    input: {
      activePointers: 3,  // Allow multiple touch points
      windowEvents: false // Prevent window event conflicts
    },
    dom: {
      createContainer: true // Enable DOM support if needed
    }
  };

  new Phaser.Game(config);
});
