# Development Guidelines for Numbers Connect Game

This document provides essential information for developers working on the Numbers Connect Game project.

## Build/Configuration Instructions

### Prerequisites
- Node.js (v14 or higher)
- npm (v6 or higher)

### Setup
1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Development
To start the development server:
```bash
npm start
```
This will start a webpack development server at http://localhost:9000 with hot reloading enabled.

### Production Build
To create a production build:
```bash
npm run build
```
This will generate optimized files in the `dist` directory, which can be deployed to a web server.

## Testing Information

### Testing Framework
This project uses <PERSON><PERSON> with TypeScript support (ts-jest) for unit testing. The testing environment is set up with jsdom to simulate a browser environment.

### Running Tests
To run all tests:
```bash
npm test
```

### Adding New Tests
1. Create test files in the `tests` directory with the naming convention `*.test.ts`
2. Import the modules you want to test
3. Write your tests using Jest's describe/it syntax
4. Run the tests to ensure they pass

### Example Test
Here's a simple example of a test for the MathUtils utility class:

```typescript
import { MathUtils } from '../src/utils/MathUtils';

describe('MathUtils', () => {
  describe('calculateDistance', () => {
    it('should calculate the distance between two points correctly', () => {
      expect(MathUtils.calculateDistance(0, 0, 3, 4)).toBe(5);
    });
  });
});
```

### Mocking Assets
When testing components that import assets (images, sounds, etc.), the test configuration automatically mocks these imports. The mock implementation is in `tests/mocks/fileMock.js`.

## Additional Development Information

### Project Structure
The project follows a modular structure:
- `src/assets`: Game assets (images, sounds, fonts)
- `src/objects`: Game object classes
- `src/scenes`: Phaser scenes (title, game, etc.)
- `src/utils`: Utility functions and classes
- `tests`: Test files

### Code Style
- Use TypeScript with strict type checking
- Follow PascalCase for class names (e.g., `NumberObject`, `GameScene`)
- Use camelCase for methods and variables (e.g., `handleNumberClick`, `isDrawing`)
- Include JSDoc comments for classes and methods
- Maintain consistent indentation (2 spaces)

### Phaser-Specific Guidelines
- Scene lifecycle: Understand the Phaser scene lifecycle (init, preload, create, update)
- Asset loading: Load assets in the preload method of scenes
- Game objects: Create reusable game objects in the `objects` directory
- Performance: Be mindful of performance, especially in the update loop

### Debugging
- Use the browser's developer tools for debugging
- Enable source maps in webpack for better debugging experience
- Use TypeScript's type checking to catch errors early

### TicTaps Integration
When integrating with the TicTaps platform:
- Use the `TicTapsConnector` class for communication with the parent window
- Implement proper event handling for game state changes
- Follow the TicTaps API documentation for integration requirements