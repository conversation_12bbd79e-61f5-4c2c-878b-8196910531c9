name: <PERSON><PERSON>
run-name: <PERSON><PERSON> run ${{ inputs.run_id }}

permissions:
  contents: write
  pull-requests: write

on:
  workflow_dispatch:
    inputs:
      run_id:
        description: "id of workflow process"
        required: true
      workflow_params:
        description: "stringified params"
        required: true

jobs:
  call-workflow-passing-data:
    uses: jetbrains-junie/junie-workflows/.github/workflows/ej-issue.yml@main
    with:
      workflow_params: ${{ inputs.workflow_params }}
