# Numbers Connect Game

A "Connect the Numbers" game built with Phaser 3 and TypeScript. Players must tap or connect numbers in sequential order (1-2-3...) to complete each round before time runs out.

## Game Mechanics

### Overview

- **Objective**: Connect numbers in sequential order (1-2-3...) as quickly as possible
- **Time Limit**: 5 minutes (300 seconds) to complete all rounds
- **Rounds**: 4 rounds with increasing difficulty
  - Round 1: 5 numbers
  - Round 2: 7 numbers
  - Round 3: 9 numbers
  - Round 4: 12 numbers

### Gameplay

1. The game starts with a countdown (3-2-1-GO)
2. Numbers appear randomly positioned on the screen
3. Tap numbers in sequential order (1, 2, 3...)
4. If you tap the wrong number, an error animation plays
5. Complete all numbers in a round to advance to the next round
6. Complete all four rounds before time runs out to win

### Controls

- **Tap Mode**: Simply tap each number in sequence
- A line is drawn connecting the numbers as you progress (SKIPPED FEATURE)

### Scoring

- Each correct number gives points equal to the current round number
- Score is multiplied by remaining time if you complete all rounds
- If time runs out before completing all rounds, you get your current score

## Project Structure

```
numbers/
├── src/                  # Source code
│   ├── assets/           # Game assets (images, sounds, fonts)
│   ├── config/           # Game configuration
│   │   └── GameConfig.ts    # Centralized game settings
│   ├── objects/          # Game object classes
│   │   └── NumberObject.ts  # Number object implementation
│   ├── scenes/           # Game scenes
│   │   ├── PreloadScene.ts  # Asset loading scene
│   │   ├── GameStartScene.ts    # Title/menu scene
│   │   ├── GameScene.ts     # Main gameplay scene
│   │   └── GameEndScene.ts  # Game over/score scene
│   ├── utils/            # Utility classes
│   │   └── TicTapsConnector.ts  # Integration with TicTaps platform
│   ├── index.html        # HTML template
│   └── index.ts          # Main entry point
├── node_modules/         # Dependencies (not in repo)
├── package.json          # Project metadata and dependencies
├── tsconfig.json         # TypeScript configuration
└── webpack.config.js     # Webpack build configuration
```

## Technical Details

### Dependencies

- **Phaser 3.60.0**: Core game framework
- **TypeScript**: For type-safe code
- **Webpack**: For bundling and development server

### Development Setup

1. Install dependencies:

   ```
   npm install
   ```

2. Start development server:

   ```
   npm start
   ```

   This will start a development server at http://localhost:9000

3. Build for production:
   ```
   npm run build
   ```
   This creates optimized files in the `dist` directory

### TicTaps Integration

The game is designed to be embedded in the TicTaps platform and communicates with the parent window using the `postMessage` API. The `TicTapsConnector` class handles:

- Notifying when the game is ready
- Sending scores to the platform
- Notifying when the game is quit

## Code Style

This project follows these coding conventions:

- **TypeScript** with strict type checking
- **Classes**: PascalCase (e.g., `NumberObject`, `GameScene`)
- **Methods/Variables**: camelCase (e.g., `handleNumberClick`, `isDrawing`)
- **Error Handling**: Graceful error handling with fallbacks
- **Comments**: JSDoc-style comments for classes and methods
- **Asset Loading**: Multiple format support for cross-browser compatibility

## Browser Compatibility

The game is designed to work in modern browsers with WebGL support. It includes:

- Multiple audio formats (OGG, MP3) for cross-browser compatibility
- Fallback mechanisms for font loading
- Error handling for asset loading failures
