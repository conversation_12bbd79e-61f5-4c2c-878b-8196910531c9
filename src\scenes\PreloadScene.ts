import Phaser from 'phaser';

export default class PreloadScene extends Phaser.Scene {
  constructor() {
    super('PreloadScene');
  }

  preload(): void {
    const { width, height } = this.cameras.main;

    // Error handler for missing assets
    this.load.on('loaderror', (fileObj: any) => {
      console.error('Failed to load asset:', fileObj.key, fileObj.src);
      // Continue even if some assets fail to load
    });

    // Specific handler for audio loading errors
    this.load.on('audioloaderror', (fileObj: any) => {
      console.warn('Audio loading failed:', fileObj.key, 'Trying fallback format');
    });

    // Load countdown images
    this.load.image('countdown-1', 'assets/images/countdown-1.png');
    this.load.image('countdown-2', 'assets/images/countdown-2.png');
    this.load.image('countdown-3', 'assets/images/countdown-3.png');
    this.load.image('countdown-go', 'assets/images/countdown-go.png');

    // Load GameStartScene Assets
    this.load.image('game_name', 'assets/images/game_name.svg');
    this.load.image('button_bg', 'assets/images/button_bg.svg');
    this.load.image('game_start', 'assets/images/game_start.png');

    // Load GameEndScene Assets
    this.load.image('game_background', 'assets/images/game_bg.png');
    this.load.image('game_over', 'assets/images/game_over.svg');
    this.load.image('back_to_lobby', 'assets/images/back_to_lobby.png');

    // Load Timer Assets
    this.load.image('timer_bg', 'assets/images/timer_bg.svg');
    this.load.image('timer_icon', 'assets/images/timer_icon.png');
    this.load.image('timer_countdown_bg', 'assets/images/timer_countdown_bg.png');

    // Load circle for NumberObject
    this.load.image('circle', 'assets/images/circle.png');

    // Load sounds with multiple format support for browser compatibility
    this.load.audio('countdown', [
      'assets/sounds/countdown.ogg',
      'assets/sounds/countdown.mp3'
    ]);
    this.load.audio('go', [
      'assets/sounds/active.ogg',
      'assets/sounds/active.mp3'
    ]);
    this.load.audio('click', [
      'assets/sounds/click.ogg',
      'assets/sounds/click.mp3'
    ]);
    this.load.audio('collect', [
      'assets/sounds/collect.ogg',
      'assets/sounds/collect.mp3'
    ]);
    this.load.audio('complete', [
      'assets/sounds/complete.ogg',
      'assets/sounds/complete.mp3'
    ]);
    this.load.audio('error', [
      'assets/sounds/error.ogg',
      'assets/sounds/error.mp3'
    ]);
    this.load.audio('timeout', [
      'assets/sounds/timeout.ogg',
      'assets/sounds/timeout.mp3'
    ]);

    // Add a completion handler to confirm all sounds are loaded
    this.load.on('filecomplete-audio', (key: string) => {
      console.log('Audio loaded:', key);
    });

    // Preload custom font (TTF)
    const fontLoader = new FontFace('NeorisTrialBold', 'url(assets/fonts/TT Neoris Trial Bold.ttf)');
    fontLoader.load().then((font) => {
      (document.fonts as any).add(font);
    }).catch((error) => {
      console.warn('Failed to load custom font, using fallback:', error);
    });

    // Create loading bar with dynamic positioning
    const progressBar = this.add.graphics();
    const progressBox = this.add.graphics();
    progressBox.fillStyle(0x222222, 0.8);

    // Dynamic loading bar positioning
    const barWidth = width * 0.6; // 60% of screen width
    const barHeight = 50;
    const barX = (width - barWidth) / 2;
    const barY = (height - barHeight) / 2;

    progressBox.fillRect(barX, barY, barWidth, barHeight);

    const loadingText = this.add.text(width / 2, height / 2, 'Loading...', {
      fontSize: '20px',
      fontFamily: 'Arial',
      color: '#ffffff'
    }).setOrigin(0.5);

    this.load.on('progress', (value: number) => {
      progressBar.clear();
      progressBar.fillStyle(0x7FFF00, 1);
      progressBar.fillRect(barX + 10, barY + 10, (barWidth - 20) * value, barHeight - 20);
    });

    this.load.on('complete', () => {
      progressBar.destroy();
      progressBox.destroy();
      loadingText.destroy();

      // Add a delay to ensure font is loaded before transitioning
      this.time.delayedCall(200, () => {
        this.scene.start('GameStartScene');
      });
    });
  }

  create(): void {
    // Transition is handled in preload complete event
  }
}
