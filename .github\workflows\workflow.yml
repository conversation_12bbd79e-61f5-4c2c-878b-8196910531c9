name: Build and Deploy Phaser Game to TicTaps

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  # Allow manual triggering
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm install

      - name: Build Phaser game
        run: npm run build

      # Zip the build for API upload
      - name: Zip Phaser build
        run: |
          cd dist
          zip -r ../build.zip .
          cd ..

      # Authenticate, upload to TicTaps API, and update game URL
      - name: Authenticate, upload, and update URL
        env:
          AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
          AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
          GAME_ID: ${{ secrets.GAME_ID }}
        run: |
          # Get access token
          echo "Getting OAuth token..."
          AUTH_RESPONSE=$(curl --location --request POST 'https://tictaps-develop.eu.auth0.com/oauth/token' \
          --header 'Content-Type: application/json' \
          --data-raw '{
            "client_id": "'"$AUTH0_CLIENT_ID"'",
            "client_secret": "'"$AUTH0_CLIENT_SECRET"'",
            "audience": "https://api.tictaps.dev/",
            "grant_type": "client_credentials"
          }')
          
          # Extract token
          ACCESS_TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.access_token')
          
          # Check if token was obtained
          if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" == "null" ]; then
            echo "Failed to get access token. Response was:"
            echo "$AUTH_RESPONSE" | jq '.'
            exit 1
          fi
          
          echo "Successfully obtained access token"
          
          # Upload game with token to v2 API
          echo "Uploading game to TicTaps API..."
          UPLOAD_RESPONSE=$(curl --location --request PUT "https://api.tictaps.dev/v2/games/$GAME_ID" \
          --header "Content-Type: multipart/form-data" \
          --header "Accept: application/json" \
          --header "Authorization: Bearer $ACCESS_TOKEN" \
          --form "game=@build.zip")
          
          echo "Upload response:"
          echo "$UPLOAD_RESPONSE" | jq '.'
          
          # Extract game URL from response
          GAME_URL=$(echo "$UPLOAD_RESPONSE" | jq -r '.data.version.gameUrl')
          echo "Original Game URL: $GAME_URL"
          
          # Fix the double https:// bug
          if [[ "$GAME_URL" == *"https://https://"* ]]; then
            echo "Detected duplicate https:// in URL, fixing..."
            FIXED_GAME_URL=$(echo "$GAME_URL" | sed 's|https://https://|https://|g')
            echo "Fixed Game URL: $FIXED_GAME_URL"
            GAME_URL="$FIXED_GAME_URL"
          fi
          
          # Update the game_url using v1 API
          echo "Updating game_url via v1 API..."
          UPDATE_RESPONSE=$(curl --location --request PATCH "https://api.tictaps.dev/v1/games/$GAME_ID" \
          --header "Content-Type: application/json" \
          --header "Accept: application/json" \
          --header "Authorization: Bearer $ACCESS_TOKEN" \
          --data-raw '{
            "game_url": "'"$GAME_URL"'"
          }')
          
          # Print raw response for debugging
          echo "Raw update response:"
          echo "$UPDATE_RESPONSE"
          
          # Check if response is valid JSON and contains 'result' field
          if echo "$UPDATE_RESPONSE" | jq -e '.result' >/dev/null 2>&1; then
            # Check if result is true
            UPDATE_RESULT=$(echo "$UPDATE_RESPONSE" | jq -r '.result')
            if [ "$UPDATE_RESULT" == "true" ]; then
              echo "✅ Game URL successfully updated via API"
              echo "Update message: $(echo "$UPDATE_RESPONSE" | jq -r '.message')"
            else
              echo "⚠️ Failed to update game URL via API. Response:"
              echo "$UPDATE_RESPONSE" | jq '.'
              exit 1
            fi
          else
            # Response is not valid JSON or doesn't contain 'result'
            echo "⚠️ Received unexpected response from API:"
            echo "$UPDATE_RESPONSE"
            exit 1
          fi