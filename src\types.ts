
/**
 * Represents the state of a number object
 */
export type NumberObjectState = 'active' | 'inactive' | 'error' | 'exploding';

/**
 * Round count mapping - defines how many numbers appear in each round
 */
export interface CountPerRound {
  [key: number]: number;
}

// ===== POSITION AND GEOMETRY TYPES =====

/**
 * 2D position coordinates
 */
export interface Position {
  x: number;
  y: number;
}

/**
 * Message types sent to TicTaps platform
 */
export type TicTapsMessageType = 'gameReady' | 'gameScore' | 'gameQuit';

/**
 * Message structure for TicTaps communication
 */
export interface TicTapsMessage {
  type: TicTapsMessageType;
  score?: number;
  data?: any;
}