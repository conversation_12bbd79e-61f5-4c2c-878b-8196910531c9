# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

```bash
npm start          # Start webpack dev server on localhost:9000
npm run build      # Production build to dist/ folder
npm test           # Run Jest tests (no tests implemented yet)
```

## Architecture Overview

This is a Phaser 3.60.0 number-tapping game built with TypeScript and webpack. The game is designed for mobile devices (540x960 resolution) and integrates with the TicTaps platform via postMessage API.

### Scene Flow
1. **PreloadScene** - Loads all game assets
2. **GameStartScene** - Splash screen with start button
3. **GameScene** - Main gameplay with 3 progressive rounds
4. **GameEndScene** - Score display and return to lobby

### Key Components

**NumberObject** (`src/objects/NumberObject.ts`)
- Extends Phaser.GameObjects.Container
- Handles number display, touch interactions, and animations
- States: active (clickable) → inactive (clicked) → exploding (destruction)

**TicTapsConnector** (`src/utils/TicTapsConnector.ts`)
- Manages communication with TicTaps platform
- Methods: sendReady(), sendScore(), sendQuit()
- Listens for platform messages (start game, etc.)

### Game Mechanics

- **3 Rounds**: 6 numbers → 9 numbers → 12 numbers
- **Time Limit**: 30 seconds per game
- **Scoring**: Points per round × time multiplier (1.0 to 0.1)
- **Goal**: Tap numbers in ascending order (1, 2, 3...)

### Platform Integration

The game communicates with TicTaps platform using postMessage:
- Sends "ready" on load
- Waits for "start" message to begin
- Reports final score on completion
- Handles "quit" for early exit